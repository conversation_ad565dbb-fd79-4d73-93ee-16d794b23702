import backtrader as bt
from xtquant import xdata, xorder, xposition, xaccount, xtrader
import plotly.graph_objects as goz
class YanTrader():
    def __init__(self, code_list, period):
        self.code_list = code_list
        self.period = period
        self.data = None
        
        
        
        
        
        
        

if __name__ == "__main__":
    # 设定一个标的列表
    code_list = ["513050.SZ"]
    # 设定获取数据的周期
    period = "1min"
    
    # 创建交易者实例
    trader = YanTrader(code_list, period)
    
    # 下载历史数据
    for code in code_list:
        xdata.download_history_data(code, period=period, incrementally=True)
    
    # 获取本地历史行情数据
    history_data = xdata.get_market_data_ex([], code_list, period=period, count=-1)
    print(history_data)