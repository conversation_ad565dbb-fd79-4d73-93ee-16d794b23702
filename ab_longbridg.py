from longport.openapi import TradeContext, Config
from longport.openapi import QuoteContext, SubType, PushQuote
from time import sleep


class YanLongPort():
    def __init__(self):
        # 初始化config
        self.long_config = Config.from_env()
        self.ctx = TradeContext(self.long_config)
        

    def account_balance(self):
        return self.ctx.account_balance()

    def get_account_balance(self):
        """
        Get account balance"""
        resp = self.ctx.account_balance()
        print(resp)
        return resp
    
    def subscribequote(self, symbols):
        """
        Subscribe to quotes for a list of symbols
        """
        def on_quote(symbol: str, quote: PushQuote):
            print(symbol, quote)
        self.ctx.set_on_quote(on_quote)
        self.ctx.subscribe(symbols, [SubType.Quote], True)
        sleep(30)

    
    
    
if __name__ == "__main__":
    yan_long_port = YanLongPort()
    balance = yan_long_port.get_account_balance()
    print("Account Balance:", balance)
    balance = yan_long_port.get_account_balance()
    print("Account Balance:", balance)
